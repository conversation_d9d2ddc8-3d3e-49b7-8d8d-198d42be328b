# Docker Setup for Agents Project

This project now includes a complete Docker setup that eliminates the need to install `uv` and manage Python dependencies locally.

## Quick Start

1. **Make the Docker script executable:**
   ```bash
   chmod +x docker.sh
   ```

2. **Build the Docker image:**
   ```bash
   ./docker.sh build
   ```

3. **Start the development environment:**
   ```bash
   ./docker.sh dev
   ./docker.sh shell
   ```

## Available Commands

### Basic Commands
- `./docker.sh build` - Build the Docker image
- `./docker.sh dev` - Start development environment
- `./docker.sh shell` - Open bash shell in container
- `./docker.sh stop` - Stop all containers
- `./docker.sh clean` - Clean up Docker resources

### Running Applications
- `./docker.sh jupyter` - Start Jupyter Lab at http://localhost:8888
- `./docker.sh gradio` - Start Gradio apps at http://localhost:7860
- `./docker.sh run <script>` - Run any Python script
- `./docker.sh notebook <path>` - Execute a specific notebook

### Package Management
- `./docker.sh install <packages>` - Install additional packages with uv

### Examples

**Run a foundation lab:**
```bash
./docker.sh run 1_foundations/app.py
```

**Execute a notebook:**
```bash
./docker.sh notebook 1_foundations/1_lab1.ipynb
```

**Install additional packages:**
```bash
./docker.sh install requests beautifulsoup4
```

**Start Jupyter for interactive development:**
```bash
./docker.sh jupyter
# Then visit http://localhost:8888
```

## Docker Services

The `docker-compose.yml` includes several services:

- **agents-dev**: Main development environment
- **jupyter**: Jupyter Lab server
- **gradio-app**: Gradio applications
- **crewai-app**: CrewAI specific applications
- **langgraph-app**: LangGraph applications

## Ports

- 8080: General web applications
- 7860: Gradio applications
- 8888: Jupyter Lab
- 8081: CrewAI applications
- 8082: LangGraph applications

## Volume Mounting

Your project directory is mounted to `/app` in the container, so all changes are synchronized between your local machine and the container.

## Environment Variables

Create a `.env` file in your project root for any environment variables (API keys, etc.):

```bash
ANTHROPIC_API_KEY=your_key_here
OPENAI_API_KEY=your_key_here
# Add other environment variables as needed
```

## Troubleshooting

**Docker not running:**
```bash
# Make sure Docker Desktop is running
docker info
```

**Permission issues:**
```bash
# The container runs as a non-root user for security
# If you encounter permission issues, try:
sudo chown -R $USER:$USER .
```

**Port conflicts:**
```bash
# If ports are already in use, modify docker-compose.yml
# Change the left side of port mappings (e.g., "8080:8080" to "8090:8080")
```

**Container won't start:**
```bash
# Check logs
./docker.sh logs

# Rebuild image
./docker.sh clean
./docker.sh build
```

## Benefits of This Setup

1. **No local dependencies**: No need to install Python, uv, or any packages locally
2. **Consistent environment**: Same Python version and packages across all machines
3. **Easy cleanup**: Remove everything with `./docker.sh clean`
4. **Multiple services**: Run different parts of your project simultaneously
5. **Development flexibility**: Full access to your code with live reloading

## Original Error Resolution

Your original "uv sync" error is now resolved because:
1. We use Python 3.12 (matching your `.python-version`)
2. All dependencies are installed in the container
3. The uv package manager is properly configured
4. System dependencies are included in the Docker image
