#!/bin/bash

# Docker management script for agents project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to build the Docker image
build() {
    print_header "🏗️  Building Docker image..."
    check_docker
    docker-compose build --no-cache
    print_status "Docker image built successfully!"
}

# Function to start development environment
dev() {
    print_header "🚀 Starting development environment..."
    check_docker
    docker-compose up -d agents-dev
    print_status "Development container started!"
    print_status "Run 'docker exec -it agents-dev bash' to enter the container"
}

# Function to start Jupyter notebook
jupyter() {
    print_header "📓 Starting Jupyter Lab..."
    check_docker
    docker-compose up -d jupyter
    print_status "Jupyter Lab started at http://localhost:8888"
}

# Function to start Gradio app
gradio() {
    print_header "🎨 Starting Gradio application..."
    check_docker
    docker-compose up -d gradio-app
    print_status "Gradio app started at http://localhost:7860"
}

# Function to run a specific notebook
notebook() {
    if [ -z "$1" ]; then
        print_error "Please specify a notebook path"
        print_status "Example: $0 notebook 1_foundations/1_lab1.ipynb"
        exit 1
    fi
    
    print_header "📋 Running notebook: $1"
    check_docker
    docker-compose run --rm agents-dev uv run jupyter nbconvert --to notebook --execute --inplace "$1"
    print_status "Notebook executed successfully!"
}

# Function to run any Python script
run() {
    if [ -z "$1" ]; then
        print_error "Please specify a Python file to run"
        print_status "Example: $0 run 1_foundations/app.py"
        exit 1
    fi
    
    print_header "🐍 Running Python script: $1"
    check_docker
    docker-compose run --rm -p 7860:7860 -p 8080:8080 agents-dev uv run python "$1"
}

# Function to open a shell in the container
shell() {
    print_header "🐚 Opening shell in development container..."
    check_docker
    if ! docker ps | grep -q agents-dev; then
        print_status "Starting development container first..."
        docker-compose up -d agents-dev
        sleep 2
    fi
    docker exec -it agents-dev bash
}

# Function to install additional packages
install() {
    if [ -z "$1" ]; then
        print_error "Please specify package(s) to install"
        print_status "Example: $0 install numpy pandas"
        exit 1
    fi
    
    print_header "📦 Installing packages: $*"
    check_docker
    docker-compose run --rm agents-dev uv add "$@"
    print_status "Packages installed successfully!"
    print_warning "You may need to rebuild the image to persist changes"
}

# Function to stop all containers
stop() {
    print_header "🛑 Stopping all containers..."
    check_docker
    docker-compose down
    print_status "All containers stopped!"
}

# Function to clean up Docker resources
clean() {
    print_header "🧹 Cleaning up Docker resources..."
    check_docker
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_status "Cleanup completed!"
}

# Function to show logs
logs() {
    local service=${1:-agents-dev}
    print_header "📋 Showing logs for $service..."
    check_docker
    docker-compose logs -f "$service"
}

# Function to show help
help() {
    print_header "🔧 Docker Management Script for Agents Project"
    echo ""
    echo "Usage: $0 [command] [arguments]"
    echo ""
    echo "Commands:"
    echo "  build              Build the Docker image"
    echo "  dev                Start development environment"
    echo "  jupyter            Start Jupyter Lab"
    echo "  gradio             Start Gradio application"
    echo "  notebook <path>    Execute a specific notebook"
    echo "  run <script>       Run a Python script"
    echo "  shell              Open bash shell in container"
    echo "  install <packages> Install additional packages with uv"
    echo "  stop               Stop all containers"
    echo "  clean              Clean up Docker resources"
    echo "  logs [service]     Show logs (default: agents-dev)"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 dev"
    echo "  $0 run 1_foundations/app.py"
    echo "  $0 notebook 1_foundations/1_lab1.ipynb"
    echo "  $0 install numpy pandas matplotlib"
    echo "  $0 shell"
}

# Main script logic
case "${1:-help}" in
    build)
        build
        ;;
    dev)
        dev
        ;;
    jupyter)
        jupyter
        ;;
    gradio)
        gradio
        ;;
    notebook)
        notebook "$2"
        ;;
    run)
        run "$2"
        ;;
    shell)
        shell
        ;;
    install)
        shift
        install "$@"
        ;;
    stop)
        stop
        ;;
    clean)
        clean
        ;;
    logs)
        logs "$2"
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
