version: '3.8'

services:
  # Main agents development environment
  agents-dev:
    build: .
    container_name: agents-dev
    volumes:
      - .:/app
      - ~/.ssh:/home/<USER>/.ssh:ro  # For git access if needed
    environment:
      - PYTHONPATH=/app
    ports:
      - "8080:8080"  # For general web apps
      - "7860:7860"  # For Gradio apps
      - "8888:8888"  # For Jupyter notebooks
    stdin_open: true
    tty: true
    command: bash
    networks:
      - agents-network

  # For running Jupyter notebooks
  jupyter:
    build: .
    container_name: agents-jupyter
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
    ports:
      - "8888:8888"
    command: uv run jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''
    networks:
      - agents-network

  # For running Gradio applications
  gradio-app:
    build: .
    container_name: agents-gradio
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
    ports:
      - "7860:7860"
    command: uv run python 1_foundations/app.py
    networks:
      - agents-network

  # For running CrewAI applications
  crewai-app:
    build: .
    container_name: agents-crewai
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
    ports:
      - "8081:8080"
    working_dir: /app/3_crew
    networks:
      - agents-network

  # For running LangGraph applications  
  langgraph-app:
    build: .
    container_name: agents-langgraph
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
    ports:
      - "8082:8080"
    command: uv run python 4_langgraph/app.py
    networks:
      - agents-network

networks:
  agents-network:
    driver: bridge

volumes:
  app-data:
